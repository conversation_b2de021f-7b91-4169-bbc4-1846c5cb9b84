import React, { useEffect, useMemo, useState } from "react";
import {
  Button,
  message,
  notification,
  Tag,
  Checkbox,
  Upload,
  Tooltip,
} from "antd";
import { UploadOutlined, DownloadOutlined } from "@ant-design/icons";
import type { UploadProps } from "antd";
import { Link, useNavigate } from "react-router-dom";
import { axiosInstance } from "../../../../apiCalls";
import axios from "axios";
import { useTableFilters } from "../../../../customHooks/useFilter";
import FilterButtons from "../../../UI/FilterButton";
import DataTable from "../../../UI/DataTable/DataTable";
import CommonPagination from "../../../UI/Pagination/commonPagination";
import SearchBox from "../../../UI/SearchBox";
import { StoreDataProps } from "../../../../types";
import { Stories } from "../../../Stores/StoreList";
import {
  ALERT_MESSAGE,
  BULK_MENU_PUSH_TO_STORES,
  DESELECT,
  FILE_NAME,
  GENERATE_MENU_PUSH_STORES,
  PROCESSING,
  S,
  SAMPLE_CSV,
  SAVING,
  SELECT_ALL,
  SELECTED,
  STORE,
  UPLOAD_CSV,
} from "../Text/Contants";

const VITE_CSV_URL= import.meta.env.VITE_CSV_URL as string;

const SingleMenuPushToStores: React.FC = () => {
  const {
    filters,
    appliedFilters,
    showClearButtons,
    handleFilterChange,
    clearFilter,
  } = useTableFilters();

  const navigate = useNavigate();

  // Core state
  const [storeOptions, setStoreOptions] = useState<StoreDataProps[]>([]);
  const [selectedStores, setSelectedStores] = useState<Set<number>>(new Set());
  // const [newlySelectedStores, setNewlySelectedStores] = useState<Set<number>>(
  //   new Set()
  // );
  // const [selectedStoreMap, setSelectedStoreMap] = useState<
  //   Map<number, StoreDataProps>
  // >(new Map());

  // UI state
  const [loading, setLoading] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);

  // Search state
  const [searchValue, setSearchValue] = useState<string>("");

  // CSV Upload state
  const [csvUploading, setCsvUploading] = useState<boolean>(false);
  const [tableRefreshKey, setTableRefreshKey] = useState<number>(0);
  const [csvUploadedStores, setCsvUploadedStores] = useState<StoreDataProps[]>([]);
  const [showCsvStoresTable, setShowCsvStoresTable] = useState<boolean>(false);

  // Memoized filters to prevent unnecessary re-renders
  const memoizedFilters = useMemo(() => filters, [filters]);

  // Computed state for select all functionality
  const isAllSelected = useMemo(() => {
    return totalCount > 0 && selectedStores.size === totalCount;
  }, [selectedStores.size, totalCount]);

  // Enhanced CSV parsing function that handles commas within quoted fields
  const parseCSV = (
    csvText: string
  ): Array<{ storeName: string; storeCode: string }> => {
    const lines = csvText.split("\n").filter((line) => line.trim());
    if (lines.length === 0) return [];

    // Function to parse a CSV line properly handling quoted fields
    const parseCSVLine = (line: string): string[] => {
      const result: string[] = [];
      let current = "";
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === "," && !inQuotes) {
          result.push(current.trim());
          current = "";
        } else {
          current += char;
        }
      }

      // Add the last field
      result.push(current.trim());
      return result.map((field) => field.replace(/^"|"$/g, "")); // Remove surrounding quotes
    };

    // Get headers (first line)
    const headers = parseCSVLine(lines[0]).map((h) => h.trim().toLowerCase());
    const storeNameIndex = headers.findIndex(
      (h) => h.includes("store") && h.includes("name")
    );
    const storeCodeIndex = headers.findIndex(
      (h) => h.includes("store") && h.includes("code")
    );

    if (storeNameIndex === -1 || storeCodeIndex === -1) {
      throw new Error('CSV must contain "Store Name" and "Store Code" columns');
    }

    // Parse data rows
    const data: Array<{ storeName: string; storeCode: string }> = [];
    for (let i = 1; i < lines.length; i++) {
      const values = parseCSVLine(lines[i]);
      if (values.length > Math.max(storeNameIndex, storeCodeIndex)) {
        const storeName = values[storeNameIndex]?.trim() || "";
        const storeCode = values[storeCodeIndex]?.trim() || "";
        if (storeName && storeCode) {
          console.log(
            `Parsed CSV row: Store Name: "${storeName}", Store Code: "${storeCode}"`
          );
          data.push({ storeName, storeCode });
        }
      }
    }
    return data;
  };

  // Function to select stores based on CSV store codes
  const selectStoresFromCSV = async (csvStoreCodes: string[]) => {
    try {
      setCsvUploading(true);

      // Get all stores to match against CSV codes
      const allStoresMap = await fetchAllStoresMap();

      // Find matching stores by code
      const matchingStoreIds: number[] = [];
      const matchingStores: StoreDataProps[] = [];
      const notFoundCodes: string[] = [];

      csvStoreCodes.forEach((code) => {
        const matchingStore = Array.from(allStoresMap.values()).find(
          (store) => store.code?.toLowerCase() === code.toLowerCase()
        );
        if (matchingStore) {
          console.log(
            `Found matching store for code ${code}: ID ${matchingStore.id}, Name: ${matchingStore.name}`
          );
          matchingStoreIds.push(matchingStore.id);
          matchingStores.push(matchingStore);
        } else {
          console.log(`No matching store found for code: ${code}`);
          notFoundCodes.push(code);
        }
      });

      // Update selections - add to existing selections instead of replacing
      setSelectedStores((prevSelected) => {
        const newSelected = new Set(prevSelected);
        matchingStoreIds.forEach((id) => newSelected.add(id));
        return newSelected;
      });

      // setNewlySelectedStores((prevNewly) => {
      //   const newNewlySelected = new Set(prevNewly);
      //   matchingStoreIds.forEach((id) => newNewlySelected.add(id));
      //   return newNewlySelected;
      // });

      // Update store map for selected stores - add to existing map
      // setSelectedStoreMap((prevMap) => {
      //   const newMap = new Map(prevMap);
      //   matchingStoreIds.forEach((id) => {
      //     const store = allStoresMap.get(id);
      //     if (store) {
      //       newMap.set(id, store);
      //     }
      //   });
      //   return newMap;
      // });

      // Store CSV uploaded stores for separate table display
      setCsvUploadedStores(matchingStores);
      setShowCsvStoresTable(matchingStores.length > 0);

      // Force table re-render to update checkboxes
      setTableRefreshKey((prev) => prev + 1);

      // Show results
      const successMessage = `Selected ${matchingStoreIds.length} stores from CSV`;
      if (notFoundCodes.length > 0) {
        message.warning(
          `${successMessage}. ${
            notFoundCodes.length
          } store codes not found: ${notFoundCodes.slice(0, 5).join(", ")}${
            notFoundCodes.length > 5 ? "..." : ""
          }`
        );
      } else {
        message.success(successMessage);
      }

      console.log("CSV Upload - Selected store IDs:", matchingStoreIds);
      console.log(
        "CSV Upload - Current selectedStores size:",
        selectedStores.size
      );
    } catch (error) {
      console.error("Error selecting stores from CSV:", error);
      message.error("Failed to process CSV data");
    } finally {
      setCsvUploading(false);
    }
  };

  // Fetch all store IDs for Select All
  const fetchAllStoreIds = async (): Promise<number[]> => {
    let page = 1;
    const size = 100;
    let allIds: number[] = [];
    const map = new Map<number, StoreDataProps>();
    let hasMore = true;

    while (hasMore) {
      const response = await axiosInstance.get<Stories>("api/stores/", {
        params: {
          page,
          page_size: size,
          ...memoizedFilters,
        },
      });

      const stores = response.data.objects;

      if (stores && stores.length > 0) {
        const ids = stores.map((store) => store.id);
        allIds = [...allIds, ...ids];

        stores.forEach((store) => {
          map.set(store.id, {
            id: store.id,
            name: store.name,
            code: store.code,
          });
        });

        if (stores.length < size) {
          hasMore = false;
        } else {
          page += 1;
        }
      } else {
        hasMore = false;
      }
    }

    // setSelectedStoreMap(map); // update for tag display
    return allIds;
  };

  // Fetch all stores with their details for CSV matching
  const fetchAllStoresMap = async (): Promise<Map<number, StoreDataProps>> => {
    let page = 1;
    const size = 100;
    const map = new Map<number, StoreDataProps>();
    let hasMore = true;

    while (hasMore) {
      try {
        const response = await axiosInstance.get<Stories>(`api/stores/`, {
          params: {
            page,
            page_size: size,
            ...memoizedFilters,
          },
        });

        const stores = response.data?.objects || [];
        stores.forEach((store: StoreDataProps) => {
          map.set(store.id, store);
        });

        hasMore = response.data?.next_page !== null;
        page++;
      } catch (error) {
        console.error(`Error fetching stores page ${page}:`, error);
        break;
      }
    }

    return map;
  };

  // CSV upload handler
  const handleCSVUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csvText = e.target?.result as string;
        const parsedData = parseCSV(csvText);

        if (parsedData.length === 0) {
          message.warning("No valid data found in CSV file");
          return;
        }

        // Extract store codes and select stores
        const storeCodes = parsedData.map((item) => item.storeCode);
        console.log("Parsed CSV store codes:", storeCodes);
        selectStoresFromCSV(storeCodes);
      } catch (error) {
        console.error("Error parsing CSV:", error);
        message.error(
          error instanceof Error ? error.message : "Failed to parse CSV file"
        );
      }
    };

    reader.onerror = () => {
      message.error("Failed to read CSV file");
    };

    reader.readAsText(file);
    return false; // Prevent default upload behavior
  };

  // Download sample CSV function
  // const downloadSampleCSV = () => {
  //   const sampleData = [
  //     ["Store Name", "Store Code"],
  //     ['"Sample Store 1"', "STORE001"],
  //     ['"Sample Store with, comma"', "STORE004"],
  //   ];

  //   const csvContent = sampleData.map((row) => row.join(",")).join("\n");
  //   const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  //   const link = document.createElement("a");

  //   if (link.download !== undefined) {
  //     const url = URL.createObjectURL(blob);
  //     link.setAttribute("href", url);
  //     link.setAttribute("download", "sample_stores.csv");
  //     link.style.visibility = "hidden";
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //   }
  // };

  const downloadSampleCSV = async() => {
    try {
      if (!VITE_CSV_URL) throw new Error("File path is undefined");
      const response = await fetch(VITE_CSV_URL, { method: "HEAD" }); // HEAD doesn't download content
    if (!response.ok) {
      if (response.status === 403) {
        console.error("File not accessible.");
       navigate("/access-denied"); // Redirect to access denied page
      } else {
        throw new Error("File not accessible.");
      }
      return;
    }

      const link = document.createElement("a");
      link.href = VITE_CSV_URL;
      link.download = FILE_NAME;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      message.success("CSV template downloaded successfully.");
    } catch (error) {
      console.error("Error exporting file:", error);
      message.error("Failed to download CSV template.");
    }
  }

  // Upload props for Ant Design Upload component
  const uploadProps: UploadProps = {
    name: "csvFile",
    accept: ".csv",
    showUploadList: false,
    beforeUpload: handleCSVUpload,
    disabled: csvUploading,
  };

  // Fetch stores for current page
  const fetchStores = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get<Stories>(`api/stores/`, {
        params: {
          page: currentPage,
          page_size: pageSize,
          ...memoizedFilters,
        },
      });

      if (response.data?.objects) {
        const fetchedStores = response.data.objects.map(
          (store: StoreDataProps) => ({
            id: store.id,
            name: store.name,
            code: store.code,
          })
        );

        setStoreOptions(fetchedStores);
        setTotalCount(response.data.total_count);

        // Update store map for the current page, but preserve already selected
        // setSelectedStoreMap((prev) => {
        //   const updated = new Map(prev);
        //   fetchedStores.forEach((store) => {
        //     if (!updated.has(store.id)) {
        //       updated.set(store.id, store);
        //     }
        //   });
        //   return updated;
        // });
      } else {
        setStoreOptions([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
      notification.error({
        message: "Error",
        description: "Failed to load stores.",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch stores on mount, pagination, or filter change
  useEffect(() => {
    fetchStores();
    // eslint-disable-next-line
  }, [currentPage, pageSize, memoizedFilters]);

  // Function to clear CSV stores table
  const clearCsvStoresTable = () => {
    setCsvUploadedStores([]);
    setShowCsvStoresTable(false);
  };

  // Cleanup function to prevent memory leaks
  useEffect(() => {
    return () => {
      setSelectedStores(new Set());
      setCsvUploadedStores([]);
      setShowCsvStoresTable(false);
      // setNewlySelectedStores(new Set());
      // setSelectedStoreMap(new Map());
    };
  }, []);

  // Handle single row checkbox change
  const handleRowCheckboxChange = (store: StoreDataProps, checked: boolean) => {
    setSelectedStores((prev) => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(store.id);
        // setSelectedStoreMap((prevMap) => {
        //   const updated = new Map(prevMap);
        //   updated.set(store.id, store);
        //   return updated;
        // });
      } else {
        newSet.delete(store.id);
      }
      return newSet;
    });
  };

  // Handle "Select All" checkbox in header
  const handleSelectAllCheckbox = async (checked: boolean) => {
    if (checked) {
      setLoading(true);
      try {
        const allStoreIds = await fetchAllStoreIds();
        setSelectedStores(new Set(allStoreIds));
        message.success(
          `Selected ${allStoreIds.length} stores across all pages`
        );
      } catch (error) {
        message.error("Failed to select all stores");
      } finally {
        setLoading(false);
      }
    } else {
      setSelectedStores(new Set());
      message.success("Deselected all stores");
    }
  };

  // Remove store from tags
  // const handleRemoveStore = (id: number) => {
  //   setSelectedStores((prev) => {
  //     const newSet = new Set(prev);
  //     newSet.delete(id);
  //     return newSet;
  //   });
  // };

  // Handle Save
  const handleStoreMapping = async () => {
    if (selectedStores.size === 0) {
      message.error("Please select at least one store.");
      return;
    }
    const finalStoreIds = Array.from(selectedStores);
    const payload = { stores: finalStoreIds };
    try {
      setIsSaving(true);
      const response = await axiosInstance.post(
        "api/menu/push-menu-to-multiple-stores/",
        payload
      );
      if (response.status === 201 || response.status === 200) {
        message.success(`${response.data.message}`);
        setSelectedStores(new Set());
      }
    } catch (error) {
      setIsSaving(false);
      console.error("Error mapping stores:", error);
      let errorMessage = "Failed to update stores.";
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          errorMessage = "Please Select at least one store.";
        } else {
          errorMessage =
            error.response?.data?.message ||
            error.response?.data?.error ||
            JSON.stringify(error.response?.data) ||
            errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      setIsSaving(false);
      message.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  // Filter, pagination, and utility handlers
  const handleSearchChange = (value: string) => {
    handleFilterChange("search", value);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearchValue("");
    }
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    setPageSize(pageSize || 999);
  };

  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";
    return text
      .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  // Table columns (no rowSelection)
  const columns = [
    {
      title: (
        <Checkbox
          checked={isAllSelected}
          indeterminate={
            selectedStores.size > 0 && selectedStores.size < totalCount
          }
          onChange={(e) => handleSelectAllCheckbox(e.target.checked)}
        >
          {isAllSelected ? `${DESELECT}` : `${SELECT_ALL}`}
        </Checkbox>
      ),
      dataIndex: "checkbox",
      key: "checkbox",
      width: "10%",
      fixed: "left" as "left",
      render: (_: any, record: StoreDataProps) => {
        const isChecked = selectedStores.has(record.id);
        // Debug logging for specific store codes
        if (record.code === "993" || record.code === "24035") {
          console.log(
            `Store ${record.code} (ID: ${record.id}) - isChecked: ${isChecked}, selectedStores size: ${selectedStores.size}`
          );
        }
        return (
          <Checkbox
            checked={isChecked}
            onChange={(e) => handleRowCheckboxChange(record, e.target.checked)}
          />
        );
      },
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "40%",
      render: (text: string, record: any) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${record.id}`}
          >
            {text}
          </Link>
        ) : (
          "N/A"
        ),
    },
    { title: "Code", dataIndex: "code", key: "code", width: "30%" },
  ];

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div className="header products-headers d-flex justify-content-between align-items-center w-100">
          <div className="title">{BULK_MENU_PUSH_TO_STORES}</div>
          <div className="ml-5">
            <Tooltip title="Download sample CSV format">
              <Button
                icon={<DownloadOutlined />}
                onClick={downloadSampleCSV}
                size="small"
                shape="round"
                type="link"
                className="btn-download-sample-csv ml-4"
              >
                {SAMPLE_CSV}
              </Button>
            </Tooltip>
          </div>
        </div>
      </div>

      <div className="d-flex flex-wrap justify-content-between align-items-center mb-3 mt-4">
        <div className="d-flex flex-wrap align-items-center">
          <div className="search-btn-driver">
            <SearchBox
              placeholder="Search by code & name"
              value={searchValue}
              onChange={setSearchValue}
              onSearch={() => handleSearchChange(searchValue)}
            />
          </div>
          <div>
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatPaymentMethod}
              filters={filters}
            />
          </div>

          {selectedStores.size > 0 && (
            <div className="ml-3">
              <Tag
                className="badge  px- py-2 fs-6 fw-bold font-family-Poppins rounded-pill"
                color="blue"
              >
                ({selectedStores.size}) {STORE}
                {selectedStores.size > 1 ? `${S}` : ``} {SELECTED}
              </Tag>
            </div>
          )}
        </div>
        <div className="d-flex flex-wrap align-items-center">
          <div className="d-flex align-items-center me-2 mb-2">
            <Tooltip title="Upload CSV file with Store Name and Store Code columns to auto-select stores">
              <Upload {...uploadProps}>
                <Button
                  className="btn-upload-csv flex-wrap"
                  icon={<UploadOutlined />}
                  loading={csvUploading}
                  disabled={csvUploading}
                   shape="round"
                  size="small"
                >
                  {csvUploading ? PROCESSING : UPLOAD_CSV}
                </Button>
              </Upload>
            </Tooltip>
          </div>

          {csvUploadedStores.length > 0 && !showCsvStoresTable && (
            <div className="d-flex align-items-center me-2 mb-2">
              <Button
                type="link"
                size="small"
                onClick={() => setShowCsvStoresTable(true)}
                className="text-primary"
              >
                Show CSV Stores ({csvUploadedStores.length})
              </Button>
            </div>
          )}

          <div className="flex-grow-1 mb-2">
            <Button
              type="primary"
               shape="round"
              className="btn-save w-100"
              onClick={handleStoreMapping}
            >
              {isSaving ? SAVING : GENERATE_MENU_PUSH_STORES}
            </Button>
          </div>
        </div>
      </div>
      <div>
        <div className="container-fluid">
          <div className="row">
            <div className="col-12">
              <div className="alert alert-warning rounded-pill text-center fw-semibold px-4 py-2 mb-3">
                {ALERT_MESSAGE}
              </div>
            </div>
          </div>
        </div>
      </div>
      <>
        {/* CSV Uploaded Stores Table */}
        {showCsvStoresTable && csvUploadedStores.length > 0 && (
          <div className="mb-4">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h5 className="mb-0 text-primary">
                Stores from CSV Upload ({csvUploadedStores.length} stores)
              </h5>
              <Button
                type="link"
                size="small"
                onClick={clearCsvStoresTable}
                className="text-danger"
              >
                Hide CSV Stores
              </Button>
            </div>
            <DataTable
              dataSource={csvUploadedStores}
              columns={[
                {
                  title: "Name",
                  dataIndex: "name",
                  key: "name",
                  width: "50%",
                  render: (text: string, record: StoreDataProps) =>
                    text ? (
                      <Link
                        className="common-link text-decoration-none"
                        to={`/stores/${record.id}`}
                      >
                        {text}
                      </Link>
                    ) : (
                      "N/A"
                    ),
                },
                {
                  title: "Code",
                  dataIndex: "code",
                  key: "code",
                  width: "30%"
                },
                {
                  title: "Status",
                  key: "status",
                  width: "20%",
                  render: (_, record: StoreDataProps) => (
                    <Tag color={selectedStores.has(record.id) ? "green" : "default"}>
                      {selectedStores.has(record.id) ? "Selected" : "Not Selected"}
                    </Tag>
                  ),
                },
              ]}
              loading={false}
              rowKey="id"
              pagination={false}
              scroll={{ x: "max-content" }}
            />
          </div>
        )}
        <div className="mt-3">
          <DataTable
            dataSource={storeOptions}
            columns={columns}
            loading={loading}
            rowKey="id"
            pagination={false}
            scroll={{ x: "max-content" }}
            key={`table-${tableRefreshKey}-${selectedStores.size}`}
          />
        </div>
        <div className="d-flex justify-content-end mt-3 mb-3">
          <CommonPagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
            showSizeChanger
          />
        </div>
      </>
    </div>
  );
};

export default SingleMenuPushToStores;
